import { Component } from "react";
import { Input, Button, Select, message, Segmented, Tag, Upload, Card, Typography } from "antd";
import {
    UserOutlined,
    InboxOutlined,
    FileTextOutlined,
    SettingOutlined,
    SendOutlined,
    ReloadOutlined
} from "@ant-design/icons";
import Cookies from "js-cookie";

import styled from "styled-components";
import CryptoJS from "crypto-js"
import moment from 'moment';

import {
    requestLeaderEmail,
    requestOpsMember,
    requestOpsMemberDutySpectrum,
    requestSuperLeaderEmails,
    requestThreeFixedCc,
    requestCommonOrder,
    requestTxCamCosAuth
} from "../../request/api";
import CcInput from "../../components/CcInput";
import withRouter from "@/util/withRouter";
import navigateModal from "@/util/navigateModal"
import { DoingOrderUrlSuffix } from "@/global"
import getFileContent from "@/util/fileHelper";

const { Option } = Select;
const { <PERSON><PERSON> } = Upload;
const { Title } = Typography;

// css-js start ↓↓↓
// 页面容器
const PageContainer = styled.div`
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;

  @media (max-width: 768px) {
    padding: 16px;
  }
`;

// 页面标题区域
const PageHeader = styled.div`
  width: 100%;
  max-width: 800px;
  margin-bottom: 24px;
  text-align: center;

  @media (max-width: 768px) {
    margin-bottom: 16px;
  }
`;

// 主要内容卡片
const MainCard = styled(Card)`
  width: 100%;
  max-width: 800px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;

  .ant-card-body {
    padding: 32px;

    @media (max-width: 768px) {
      padding: 20px;
    }
  }

  @media (max-width: 768px) {
    border-radius: 8px;
    margin: 0 -8px;
  }
`;

// 表单区域
const FormSection = styled.div`
  margin-bottom: 32px;
  padding: 24px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e8e8e8;

  &:last-child {
    margin-bottom: 0;
  }

  &:first-child {
    background: #f8faff;
    border-color: #e1e8ff;
  }
`;

// 区域标题
const SectionTitle = styled.div`
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20px;
  display: flex;
  align-items: center;

  .anticon {
    margin-right: 8px;
    color: #3b82f6;
  }
`;

// 表单行
const FormRow = styled.div`
  display: flex;
  align-items: ${props => props.alignTop ? 'flex-start' : 'center'};
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    margin-bottom: 16px;
  }
`;

// 标签样式
const FormLabel = styled.div`
  width: 100px;
  text-align: right;
  padding-right: 16px;
  font-weight: 500;
  color: #374151;
  flex-shrink: 0;
  line-height: 40px;

  ${props => props.alignTop && `
    padding-top: 8px;
    line-height: 1.5;
  `}

  &::after {
    content: '*';
    color: #ef4444;
    margin-left: 4px;
    display: ${props => props.required ? 'inline' : 'none'};
  }

  @media (max-width: 768px) {
    width: 100%;
    text-align: left;
    padding-right: 0;
    padding-bottom: 8px;
    line-height: 1.5;
  }
`;

// 输入区域
const FormContent = styled.div`
  flex: 1;
  min-width: 0;

  @media (max-width: 768px) {
    width: 100%;
  }
`;

// 按钮区域
const ActionSection = styled.div`
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
`;

// 全局样式注入
const GlobalStyles = styled.div`
  .upload-dragger:hover {
    border-color: #3b82f6 !important;
    background-color: #f0f9ff !important;
  }

  .ant-segmented {
    background-color: #f1f5f9;
    border-radius: 8px;
    padding: 4px;

    @media (max-width: 768px) {
      width: 100%;

      .ant-segmented-item {
        flex: 1;
        text-align: center;
      }
    }
  }

  .ant-segmented-item {
    border-radius: 6px;
    font-weight: 500;
  }

  .ant-segmented-item-selected {
    background-color: #3b82f6;
    color: white;
  }

  .ant-select-large .ant-select-selector {
    border-radius: 6px;
  }

  .ant-input-affix-wrapper-lg,
  .ant-input-lg {
    border-radius: 6px;
  }

  .ant-btn-lg {
    font-weight: 500;
  }

  @media (max-width: 768px) {
    .ant-select {
      width: 100% !important;
      max-width: none !important;
    }

    .ant-input {
      width: 100% !important;
      max-width: none !important;
    }

    .ant-tag {
      margin-bottom: 4px;
    }
  }
`;

// css-js end   ↑↑↑
class CommonOrderForm extends Component {

    // 默认展示所有审批选项，包含三级领导
    audit_schema = [
        { label: '部门领导', value: 'common' },
        { label: '两级领导', value: 'common_super_leader' },
        { label: '三级领导', value: 'common_three_level_leader' },
        { label: '指定审批人', value: 'common_pointed_approver' }
    ]
    ignore_new_sys_apply_template_txt = "新系统资源申请请参考模板 https://cheetahfun.feishu.cn/sheets/shtcnJgMCWDI98bh6SoB0mDVvBd，新建新的资源申请飞书文档。然后将文档的链接贴在该文本框内，并在文本框内写明申请理由。"
    new_sys_apply_template_txt =
        `新系统资源申请请参考模板 https://cheetahfun.feishu.cn/sheets/shtcnJgMCWDI98bh6SoB0mDVvBd，新建新的资源申请飞书文档。然后将文档的链接贴在该文本框内，并在文本框内写明申请理由。

申请理由：
新系统资源详情表(飞书文档链接)：`
    constructor(props) {
        super(props);
        
        // 预加载部门领导和二级领导数据，这些请求会被缓存
        this.leaderPromise = requestLeaderEmail({});
        this.superLeaderPromise = requestSuperLeaderEmails({});
        // 预取三级审批固定抄送人以热缓存，避免用户切换时再发起请求
        try {
            requestThreeFixedCc().catch(() => {});
        } catch (e) { /* noop */ }
    }
    
    state = {
        show_pointed_approver: false,
        pointed_approver: "",
        order_type: this.audit_schema[0].value,
        title: '',
        apply_msg: "",
        ops_audit_email: ["自动分配"],
        selected_ops_audit_email: "自动分配",
        leader_emails: undefined,
        dutySpectrumMemberMap: {},
        file_infos: [],
        showCcInput: false,
        ccList: [],
        // 三级审批固定抄送人列表（不可移除）
        fixedCcList: []
    };
    
    componentDidMount() {
        // 使用已预加载的部门领导数据
        const leaderPromise = this.leaderPromise.then((data) => {
            this.setState({
                leader_emails: data.email,
            });
            return data;
        });
        
        const opsPromise = requestOpsMember({}).then((data) => {
            this.setState({
                ops_audit_email: [...this.state.ops_audit_email, ...data.members],
            });
            return data;
        });
        
        const dutyPromise = requestOpsMemberDutySpectrum({}).then((data) => {
            this.setState({
                dutySpectrumMemberMap: data.duty_member_map
            });
            return data;
        });
        
        // 等待数据加载完成
        Promise.all([this.superLeaderPromise, leaderPromise, opsPromise, dutyPromise])
            .catch(error => {
                console.error('加载数据时出错:', error);
            });
    }
    componentWillUnmount = () => {
        this.setState = (state, callback) => {
            return;
        };
    };
    handleTitleChange = (e) => {
        this.setState({
            title: e.target.value,
        });
    };
    handleApplyMsgChange = (e) => {
        this.setState({
            apply_msg: e.target.value,
        });
    };
    handleAuditEmailChange = (value) => {
        this.setState({
            selected_ops_audit_email: value,
        });
    };
    // 处理职责变更
    handleDutyChange = (value) => {
        if (value === "新系统上线资源申请") {
            this.setState({
                apply_msg: this.new_sys_apply_template_txt
            })
        }
        // 如果职责对应多人，则随机分配一人
        this.setState({
            selected_ops_audit_email: this.state.dutySpectrumMemberMap[value][Math.floor(Math.random() * this.state.dutySpectrumMemberMap[value].length)],
        });
    }
    // 文件上传
    handleFileUpload = (options) => {
        getFileContent(options.file).then((result) => {
            var wordArray = CryptoJS.lib.WordArray.create(result);
            var md5 = CryptoJS.MD5(wordArray).toString();
            // 获取日期
            let dateDir = moment().format('YYYY-MM-DD');
            let filePath = `commonOrderAttachment/${dateDir}/${md5}_${options.file.name}`
            // 获取腾讯云 cos 临时票据
            requestTxCamCosAuth({}).then((data) => {
                var COS = require('cos-js-sdk-v5');
                var cos = new COS({
                    SecurityToken: data.TmpToken,
                    SecretId: data.TmpSecretId,
                    SecretKey: data.TmpSecretKey
                });
                cos.putObject({
                    Bucket: 'ops-1252921383',   /* 必须 */
                    Region: 'ap-guangzhou',     /* 存储桶所在地域，必须字段 */
                    Key: filePath,              /* 必须 */
                    StorageClass: 'STANDARD',
                    Body: options.file, // 上传文件对象
                    onProgress: function (progressData) {
                        options.onProgress({ percent: progressData.percent * 100 })
                    }
                }, (err, data) => {
                    if (err === null) {
                        this.setState({
                            file_infos: [...this.state.file_infos, { "path": filePath, "name": options.file.name }]
                        })
                        options.onSuccess(data)
                        console.log({ "path": filePath, "file_name": options.file.name })
                    } else {
                        options.onError(err)
                    }
                });
            })
        })
    }
    // 处理抄送人变化
    handleCcChange = (newList) => {
        this.setState({ ccList: newList });
    };

    // 处理删除单个抄送人员
    handleRemoveCc = (emailToRemove) => {
        const newList = this.state.ccList.filter(user => user.email !== emailToRemove);
        this.setState({ ccList: newList });
    };

    // 处理重置按钮
    resetHandle = () => {
        this.setState({
            order_type: this.audit_schema[0].value,
            title: '',
            apply_msg: "",
            selected_ops_audit_email: "自动分配",
            pointed_approver: "",
            ccList: [],
            showCcInput: false,
            fixedCcList: []
        });
    };
    handleSubmit = () => {
        // 验证标题不能为空或仅包含空格
        const trimmedTitle = (this.state.title || '').trim();
        if (trimmedTitle.length <= 0) {
            message.warn("请输入有效标题", 1);
            return;
        }
        if (this.state.apply_msg.length <= 5) {
            message.warn("需求详叙述不能少于等于5个字", 1);
            return;
        }
        const apply_info = {
            title: this.state.title,
            ops_audit_email: this.state.selected_ops_audit_email,
        };
        if (this.state.order_type === this.audit_schema[3].value) {
            if (this.state.pointed_approver.length === 0) {
                message.warn("审批人员邮箱不能为空", 1);
                return;
            }
            apply_info.approver_email = this.state.pointed_approver
        }
        var apply_msg_filted = this.state.apply_msg
        apply_msg_filted = apply_msg_filted.replace(this.ignore_new_sys_apply_template_txt, '');
        var hasSpecialCharacters = /['"\t]/.test(apply_msg_filted);

        if (hasSpecialCharacters) {
            message.warn(`申请文本存在非法字符（"'\t），已自动去除，请知悉。`, 5);
            apply_msg_filted = apply_msg_filted.replace(/['"\t]/g, '');
        }
        // 构造抄送人信息（包含固定抄送+用户选择抄送）
        const mergedCcList = [...(this.state.fixedCcList || []), ...(this.state.ccList || [])];
        const cc_user_infos = mergedCcList.map(user => ({
            cc_open_id: user.open_id,
            cc_email: user.email || null
        }));

        const args = {
            apply_info: JSON.stringify(apply_info),
            order_type: this.state.order_type,
            title: this.state.title,
            apply_msg: apply_msg_filted,
            attachment_info: this.state.file_infos,
            cc_user_infos: cc_user_infos
        };
        requestCommonOrder(args).then((data) => {
            navigateModal(this.props.navigate, DoingOrderUrlSuffix, 3)
        });
    };
    handleOrderTypeChange = (value) => {
        this.setState({
            pointed_approver: "",
        });
        let show_pointed_approver = false;

        // 判断是否是指定审批人选项
        if (value === 'common_pointed_approver') {
            show_pointed_approver = true;
            // 指定审批人模式下不应存在固定抄送
            this.setState({ fixedCcList: [] });
        } else {
            // 根据不同审批选项获取对应的领导邮箱
            if (value === 'common') { // 部门领导
                // 使用已缓存的部门领导数据
                this.leaderPromise.then((data) => {
                    this.setState({
                        leader_emails: data.email,
                        // 切换到非三级审批时清空固定抄送
                        fixedCcList: []
                    });
                });
            } else if (value === 'common_super_leader') { // 两级领导
                // 使用已缓存的二级领导数据
                this.superLeaderPromise.then((data) => {
                    this.setState({
                        leader_emails: data.emails.join(","),
                        // 切换到非三级审批时清空固定抄送
                        fixedCcList: []
                    });
                });
            } else if (value === 'common_three_level_leader') { // 三级领导
                this.superLeaderPromise.then((data) => {
                    const threeLevelEmails = data.emails.join(",") + ",<EMAIL>";
                    this.setState({
                        leader_emails: threeLevelEmails,
                    });
                });
                requestThreeFixedCc().then((resp) => {
                    if (resp && resp.resp_common && resp.resp_common.ret === 0) {
                        const currentEmail = Cookies.get("user_email");
                        const users = Array.isArray(resp.fixed_cc) ? resp.fixed_cc : [];
                        const fixedList = users
                            .filter(u => !u.email || u.email !== currentEmail)
                            .map(u => ({
                                is_fixed: true,
                                open_id: u.open_id,
                                email: u.email || null,
                                name: u.name,
                                department: u.department,
                                display_text: u.department ? `${u.name}-${u.department}` : u.name,
                                full_info: u.department ? `${u.name}-${u.department}` : u.name
                            }));
                        this.setState({ fixedCcList: fixedList });
                    } else {
                        this.setState({ fixedCcList: [] });
                    }
                }).catch(() => {
                    this.setState({ fixedCcList: [] });
                });
            }
        }

        this.setState({
            order_type: value,
            show_pointed_approver: show_pointed_approver
        })
    }
    render() {
        return (
            <GlobalStyles>
                <PageContainer>
                    <PageHeader>
                        <Title level={2} style={{ margin: 0, color: '#1f2937' }}>
                            <FileTextOutlined style={{ marginRight: 12, color: '#3b82f6' }} />
                            通用运维工单
                        </Title>
                    </PageHeader>

                <MainCard>
                    {/* 基本信息区域 */}
                    <FormSection>
                        <SectionTitle>
                            <SettingOutlined />
                            基本信息
                        </SectionTitle>
                        <FormRow>
                            <FormLabel>业务类型：</FormLabel>
                            <FormContent>
                                <Select
                                    placeholder="请选择业务类型"
                                    onChange={this.handleDutyChange}
                                    style={{ width: '100%', maxWidth: '300px' }}
                                    size="large"
                                >
                                    {
                                        Object.keys(this.state.dutySpectrumMemberMap).map((duty) => {
                                            return (
                                                <Option value={duty} key={duty}>
                                                    {duty}
                                                </Option>
                                            );
                                        })}
                                </Select>
                            </FormContent>
                        </FormRow>

                        <FormRow>
                            <FormLabel>运维审批：</FormLabel>
                            <FormContent>
                                <Select
                                    placeholder="请选择运维审批人"
                                    defaultValue={this.state.ops_audit_email[0]}
                                    value={this.state.selected_ops_audit_email}
                                    onChange={this.handleAuditEmailChange}
                                    style={{ width: '100%', maxWidth: '300px' }}
                                    size="large"
                                >
                                    {this.state.ops_audit_email.map((email) => {
                                        return (
                                            <Option value={email} key={email}>
                                                {email}
                                            </Option>
                                        );
                                    })}
                                </Select>
                            </FormContent>
                        </FormRow>

                        <FormRow>
                            <FormLabel required>审批模式：</FormLabel>
                            <FormContent>
                                <Segmented
                                    options={this.audit_schema}
                                    onChange={this.handleOrderTypeChange}
                                    value={this.state.order_type}
                                    size="large"
                                    style={{ width: 'fit-content' }}
                                />
                            </FormContent>
                        </FormRow>

                        <FormRow>
                            <FormLabel>审批人员：</FormLabel>
                            <FormContent>
                                <div style={{ display: 'flex', alignItems: 'center' }}>
                                    <Tag
                                        color="#3b82f6"
                                        icon={<UserOutlined />}
                                        className={this.state.show_pointed_approver ? "hide" : ""}
                                        style={{
                                            maxWidth: '100%',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            padding: '8px 12px',
                                            fontSize: '14px',
                                            borderRadius: '6px',
                                            border: 'none'
                                        }}
                                    >
                                        {this.state.leader_emails}
                                    </Tag>
                                    <Input
                                        placeholder="请输入审批人邮箱"
                                        size="large"
                                        className={!this.state.show_pointed_approver ? "hide" : ""}
                                        value={this.state.pointed_approver}
                                        style={{ width: '100%', maxWidth: '400px' }}
                                        onChange={(e) => { this.setState({ pointed_approver: e.target.value }) }}
                                    />
                                </div>
                            </FormContent>
                        </FormRow>
                    </FormSection>

                    {/* 工单内容区域 */}
                    <FormSection>
                        <SectionTitle>
                            <FileTextOutlined />
                            工单内容
                        </SectionTitle>
                        <FormRow>
                            <FormLabel required>标题：</FormLabel>
                            <FormContent>
                                <Input
                                    placeholder="请输入工单标题"
                                    value={this.state.title}
                                    onChange={this.handleTitleChange}
                                    size="large"
                                    style={{ width: '100%' }}
                                />
                            </FormContent>
                        </FormRow>

                        <FormRow alignTop>
                            <FormLabel alignTop required>需求描述：</FormLabel>
                            <FormContent>
                                <Input.TextArea
                                    placeholder="请详细描述您的需求，包括具体要求、预期结果等..."
                                    value={this.state.apply_msg}
                                    onChange={this.handleApplyMsgChange}
                                    rows={8}
                                    style={{
                                        width: '100%',
                                        resize: 'vertical',
                                        borderRadius: '6px'
                                    }}
                                />
                            </FormContent>
                        </FormRow>
                    </FormSection>

                    {/* 附件上传区域 */}
                    <FormSection>
                        <SectionTitle>
                            <InboxOutlined />
                            附件上传
                        </SectionTitle>
                        <FormRow alignTop>
                            <FormLabel alignTop>文件上传：</FormLabel>
                            <FormContent>
                                <Dragger
                                    customRequest={this.handleFileUpload}
                                    style={{
                                        width: '100%',
                                        minHeight: "140px",
                                        borderRadius: '8px',
                                        border: '2px dashed #cbd5e1',
                                        backgroundColor: '#f8fafc',
                                        transition: 'all 0.3s ease'
                                    }}
                                    className="upload-dragger"
                                >
                                    <p className="ant-upload-drag-icon">
                                        <InboxOutlined style={{ fontSize: '48px', color: '#3b82f6' }} />
                                    </p>
                                    <p className="ant-upload-text" style={{
                                        fontSize: '16px',
                                        color: '#374151',
                                        fontWeight: 500,
                                        margin: '8px 0 4px 0'
                                    }}>
                                        单击或拖动文件到此区域上传
                                    </p>
                                    <p className="ant-upload-hint" style={{
                                        fontSize: '14px',
                                        color: '#6b7280',
                                        margin: 0
                                    }}>
                                        支持单个或批量上传，审批时小文本可在线浏览，其他文件下载查看
                                    </p>
                                </Dragger>
                            </FormContent>
                        </FormRow>
                    </FormSection>

                    {/* 抄送功能区域 */}
                    <FormSection>
                        <FormRow alignTop>
                            <FormLabel alignTop>抄送：</FormLabel>
                            <FormContent>
                                <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: '8px' }}>
                                    <Button
                                        type="link"
                                        onClick={() => this.setState({ showCcInput: !this.state.showCcInput })}
                                        style={{
                                            padding: 0,
                                            height: 'auto',
                                            marginLeft: 0,
                                            color: '#3b82f6',
                                            fontWeight: 500
                                        }}
                                    >
                                        {this.state.showCcInput ? '收起' : '[添加抄送]'}
                                    </Button>
                                    {/* 收起时展示固定抄送（不可移除）+ 用户抄送预览 */}
                                    {!this.state.showCcInput && (
                                        <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: '6px', marginTop: '8px' }}>
                                            {/* 固定抄送：全部展示，不可移除 */}
                                            {(this.state.fixedCcList || []).map(user => (
                                                <Tag
                                                    key={user.open_id}
                                                    title={user.full_info || user.display_text}
                                                    style={{
                                                        margin: 0,
                                                        cursor: 'default',
                                                        userSelect: 'none',
                                                        maxWidth: '120px',
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis',
                                                        whiteSpace: 'nowrap',
                                                        borderRadius: '6px',
                                                        backgroundColor: '#f0f9ff',
                                                        borderColor: '#3b82f6',
                                                        color: '#1e40af'
                                                    }}
                                                >
                                                    {user.display_text || (user.department ? `${user.name}-${user.department}` : user.name)}
                                                </Tag>
                                            ))}
                                            {/* 用户抄送：最多展示3个，可移除 */}
                                            {this.state.ccList.slice(0, 3).map((user) => (
                                                <Tag
                                                    key={user.email}
                                                    closable
                                                    onClose={() => this.handleRemoveCc(user.email)}
                                                    title={user.full_info || user.display_text}
                                                    style={{
                                                        margin: 0,
                                                        cursor: 'default',
                                                        userSelect: 'none',
                                                        maxWidth: '120px',
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis',
                                                        whiteSpace: 'nowrap',
                                                        borderRadius: '6px'
                                                    }}
                                                >
                                                    {user.display_text}
                                                </Tag>
                                            ))}
                                            {(() => {
                                                const fixedCount = (this.state.fixedCcList || []).length;
                                                const userCount = this.state.ccList.length;
                                                const displayed = fixedCount + Math.min(3, userCount);
                                                const total = fixedCount + userCount;
                                                const hidden = Math.max(0, total - displayed);
                                                return hidden > 0 ? (
                                                    <Tag
                                                        style={{
                                                            margin: 0,
                                                            cursor: 'pointer',
                                                            userSelect: 'none',
                                                            backgroundColor: '#f0f0f0',
                                                            border: '1px dashed #d9d9d9',
                                                            color: '#666',
                                                            borderRadius: '6px'
                                                        }}
                                                        onClick={() => this.setState({ showCcInput: true })}
                                                        title={`点击查看全部${total}个抄送人员`}
                                                    >
                                                        +{hidden}个
                                                    </Tag>
                                                ) : null;
                                            })()}
                                        </div>
                                    )}
                                </div>
                                {this.state.showCcInput && (
                                    <div style={{ marginTop: '12px' }}>
                                        {/* 展开时先展示固定抄送（不可移除） */}
                                        {(this.state.fixedCcList || []).length > 0 && (
                                            <div style={{ marginBottom: '12px', display: 'flex', gap: '6px', flexWrap: 'wrap' }}>
                                                {this.state.fixedCcList.map(user => (
                                                    <Tag
                                                        key={user.open_id}
                                                        title={user.full_info || user.display_text}
                                                        style={{
                                                            margin: 0,
                                                            cursor: 'default',
                                                            userSelect: 'none',
                                                            borderRadius: '6px',
                                                            backgroundColor: '#f0f9ff',
                                                            borderColor: '#3b82f6',
                                                            color: '#1e40af'
                                                        }}
                                                    >
                                                        {user.display_text || (user.department ? `${user.name}-${user.department}` : user.name)}
                                                    </Tag>
                                                ))}
                                            </div>
                                        )}
                                        <CcInput
                                            value={this.state.ccList}
                                            onChange={this.handleCcChange}
                                            currentUserEmail={Cookies.get("user_email")}
                                            fixedCount={(this.state.fixedCcList || []).length}
                                            fixedEmails={(this.state.fixedCcList || []).map(u => u.email).filter(Boolean)}
                                        />
                                    </div>
                                )}
                            </FormContent>
                        </FormRow>
                    </FormSection>

                    <ActionSection>
                        <Button
                            type="primary"
                            size="large"
                            icon={<SendOutlined />}
                            onClick={this.handleSubmit}
                            style={{
                                borderRadius: '8px',
                                height: '44px',
                                paddingLeft: '24px',
                                paddingRight: '24px',
                                fontWeight: 500
                            }}
                        >
                            提交工单
                        </Button>
                        <Button
                            size="large"
                            icon={<ReloadOutlined />}
                            onClick={this.resetHandle}
                            style={{
                                borderRadius: '8px',
                                height: '44px',
                                paddingLeft: '24px',
                                paddingRight: '24px'
                            }}
                        >
                            重置
                        </Button>
                    </ActionSection>
                </MainCard>
            </PageContainer>
            </GlobalStyles>
        );
    }
}

export default withRouter(CommonOrderForm)